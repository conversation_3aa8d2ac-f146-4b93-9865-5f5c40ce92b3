import createAxios from '/@/utils/mceAxios'

export const productUrl = '/mce/Dashboard/'

/**
 * 获取商品列表
 */
export function getProducts(params: {
    category?: string
    keyword?: string
} = {}) {
    return createAxios({
        url: productUrl + 'getProducts',
        method: 'get',
        params: params,
    })
}

/**
 * 获取商品详情
 */
export function getProductDetail(productCode: string) {
    return createAxios({
        url: productUrl + 'getProductDetail',
        method: 'get',
        params: {
            productCode: productCode
        },
    })
}

/**
 * 获取商品分类列表
 */
export function getCategories() {
    return createAxios({
        url: productUrl + 'getCategories',
        method: 'get',
    })
}
