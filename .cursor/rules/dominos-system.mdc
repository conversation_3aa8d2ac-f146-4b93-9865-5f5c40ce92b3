---
description: 达美乐代下系统
globs: 
alwaysApply: false
---
# 达美乐代下系统 - Cursor规则

## 项目概述
这是一个基于BuildAdmin框架开发的达美乐代下系统，采用前后端分离架构。

## 技术栈
- **后端**: ThinkPHP 8.1 + PHP 8.0+
- **前端**: Vue 3.x + TypeScript + Vite + Pinia + Element Plus
- **数据库**: MySQL
- **其他**: Workerman(常驻内存)、支付模块、短信模块等

## 项目结构

### 后端结构
- [app/](mdc:app) - 应用主目录
  - [app/admin/](mdc:app/admin) - 后台管理应用
  - [app/api/](mdc:app/api) - API接口应用
  - [app/common/](mdc:app/common) - 公共模块
  - [app/mce/](mdc:app/mce) - MCE模块
- [modules/](mdc:modules) - 功能模块
  - [modules/pay/](mdc:modules/pay) - 支付模块
  - [modules/sms/](mdc:modules/sms) - 短信模块
  - [modules/mail/](mdc:modules/mail) - 邮件模块
  - [modules/wxpush/](mdc:modules/wxpush) - 微信推送模块
  - [modules/backups/](mdc:modules/backups) - 备份模块
  - [modules/plantask/](mdc:modules/plantask) - 计划任务模块
- [config/](mdc:config) - 配置文件
- [database/](mdc:database) - 数据库迁移文件
- [extend/](mdc:extend) - 扩展类库
- [public/](mdc:public) - 公共资源目录
- [runtime/](mdc:runtime) - 运行时目录

### 前端结构
- [web/](mdc:web) - 前端项目根目录
  - [web/src/](mdc:web/src) - 源代码
  - [web/public/](mdc:web/public) - 静态资源
  - [web/types/](mdc:web/types) - TypeScript类型定义

## 核心配置文件
- [composer.json](mdc:composer.json) - PHP依赖管理
- [web/package.json](mdc:web/package.json) - 前端依赖管理
- [config/buildadmin.php](mdc:config/buildadmin.php) - BuildAdmin框架配置
- [config/database.php](mdc:config/database.php) - 数据库配置
- [config/pay.php](mdc:config/pay.php) - 支付配置
- [config/sms.php](mdc:config/sms.php) - 短信配置

## 开发指南

### 后端开发
1. **控制器**: 继承 [app/BaseController.php](mdc:app/BaseController.php)
2. **模型**: 放在对应应用的model目录下
3. **服务类**: 放在对应应用的service目录下
4. **中间件**: 配置在 [app/middleware.php](mdc:app/middleware.php)
5. **路由**: 自动注册，无需手动配置

### 前端开发
1. **页面组件**: 放在 [web/src/views/](mdc:web/src/views) 目录
2. **API接口**: 放在 [web/src/api/](mdc:web/src/api) 目录
3. **状态管理**: 使用Pinia，放在 [web/src/stores/](mdc:web/src/stores) 目录
4. **工具函数**: 放在 [web/src/utils/](mdc:web/src/utils) 目录

### 模块开发
- 新模块应放在 [modules/](mdc:modules) 目录下
- 模块应包含完整的MVC结构
- 模块配置放在 [config/](mdc:config) 目录下

## 常用命令
```bash
# 后端
php think migrate:run          # 运行数据库迁移
php think make:controller      # 生成控制器
php think make:model          # 生成模型
php think make:service        # 生成服务类

# 前端
cd web
npm install                   # 安装依赖
npm run dev                   # 开发环境
npm run build                 # 生产构建
```

## 注意事项
1. 遵循PSR-4自动加载规范
2. 使用ThinkPHP 8.1的新特性
3. 前端使用Vue 3 Composition API
4. 数据库操作使用ORM
5. API接口需要权限验证
6. 前端路由需要权限控制

## 特色功能
- **CRUD代码生成**: 可视化拖拽生成后台增删改查代码
- **WEB终端**: 内置终端，支持命令执行
- **权限管理**: 可视化权限管理，支持无限子级
- **数据回收站**: 全局数据回收和字段级修改记录
- **模块市场**: 一键安装功能模块
- **常驻内存**: 支持Workerman常驻内存运行

