---
description: 
globs: 
alwaysApply: false
---
# 达美乐代下系统 - Cursor规则总览

## 规则文件说明

本项目包含以下Cursor规则文件，用于指导AI助手更好地理解和协助开发达美乐代下系统：

### 1. [dominos-system.mdc](mdc:dominos-system.mdc) - 系统架构规则
- **用途**: 描述项目整体架构、技术栈和目录结构
- **内容**: 
  - 项目概述和技术栈
  - 后端和前端目录结构
  - 核心配置文件说明
  - 开发指南和常用命令
  - 特色功能介绍

### 2. [dominos-business.mdc](mdc:dominos-business.mdc) - 业务逻辑规则
- **用途**: 定义核心业务模块和业务逻辑
- **内容**:
  - 用户管理、订单管理、商品管理
  - 支付系统、配送系统、营销系统
  - 数据库设计要点
  - API接口规范
  - 业务逻辑注意事项

### 3. [development-standards.mdc](mdc:development-standards.mdc) - 开发规范
- **用途**: 制定代码规范和最佳实践
- **内容**:
  - PHP和前端代码规范
  - 目录结构和命名规范
  - 注释和错误处理规范
  - 安全规范和性能优化
  - 测试规范和部署规范

### 4. [api-documentation.mdc](mdc:api-documentation.mdc) - API文档规则
- **用途**: 定义API接口规范和文档格式
- **内容**:
  - API接口规范和响应格式
  - 用户、商品、订单、支付相关接口
  - 错误处理和接口限流
  - 版本控制和文档更新

## 使用指南

### 对于AI助手
这些规则文件将帮助AI助手：
1. **理解项目结构**: 快速了解项目的技术栈和目录结构
2. **遵循开发规范**: 按照既定的代码规范和最佳实践进行开发
3. **实现业务逻辑**: 根据业务规则实现正确的功能
4. **编写API接口**: 按照API文档规范编写接口
5. **提供准确建议**: 基于项目特点提供针对性的建议

### 对于开发者
这些规则文件将帮助开发者：
1. **快速上手**: 了解项目架构和开发流程
2. **保持一致性**: 遵循统一的代码规范和命名约定
3. **提高效率**: 使用预定义的模板和最佳实践
4. **减少错误**: 避免常见的开发陷阱和安全问题

## 规则文件维护

### 更新原则
- 当项目架构发生变化时，更新 `dominos-system.mdc`
- 当业务逻辑发生变化时，更新 `dominos-business.mdc`
- 当开发规范发生变化时，更新 `development-standards.mdc`
- 当API接口发生变化时，更新 `api-documentation.mdc`

### 版本控制
- 所有规则文件的修改都应该通过Git进行版本控制
- 重要的规则变更应该在提交信息中说明
- 定期审查和更新规则文件，确保其与项目现状保持一致

## 技术栈回顾

### 后端技术栈
- **框架**: ThinkPHP 8.1
- **语言**: PHP 8.0+
- **数据库**: MySQL
- **缓存**: Redis
- **队列**: Workerman
- **支付**: 微信支付、支付宝
- **短信**: 阿里云短信

### 前端技术栈
- **框架**: Vue 3.x
- **语言**: TypeScript
- **构建工具**: Vite
- **状态管理**: Pinia
- **UI组件**: Element Plus
- **路由**: Vue Router
- **HTTP客户端**: Axios

## 项目特色

### BuildAdmin框架特色
- **CRUD代码生成**: 可视化拖拽生成后台增删改查代码
- **WEB终端**: 内置终端，支持命令执行
- **权限管理**: 可视化权限管理，支持无限子级
- **数据回收站**: 全局数据回收和字段级修改记录
- **模块市场**: 一键安装功能模块
- **常驻内存**: 支持Workerman常驻内存运行

### 达美乐代下系统特色
- **用户管理**: 完整的用户注册、登录、信息管理
- **订单系统**: 完整的订单创建、跟踪、管理流程
- **支付系统**: 多种支付方式，安全可靠
- **配送系统**: 基于地理位置的配送管理
- **营销系统**: 优惠券、积分、推荐奖励等营销功能

## 联系方式

如有问题或建议，请联系：
- 技术支持：<EMAIL>
- 文档地址：https://doc.dominos.cm
- 开发者社区：https://community.dominos.cm

