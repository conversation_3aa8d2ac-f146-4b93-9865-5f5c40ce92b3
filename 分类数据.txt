[{"categoryCode": "81", "categoryLevel": "1", "categoryName": "潮新品", "categoryNameEN": " New Items", "childNode": [{"categoryCode": "82", "categoryLevel": "2", "categoryName": "潮新品", "categoryNameEN": " New Items", "childNode": [], "imgUrl": "", "isShowImage": 0}], "imgUrl": "https://pic.dominos.com.cn:8443/ApiPicture/20220306/9206de6f1b2b4e878224f5a1b1899689.png", "isShowImage": 0}, {"categoryCode": "130", "categoryLevel": "1", "categoryName": "可可熔岩", "categoryNameEN": "coco", "childNode": [{"categoryCode": "131", "categoryLevel": "2", "categoryName": "可可熔岩", "categoryNameEN": "coco", "childNode": [], "imgUrl": "", "isShowImage": 0}], "imgUrl": "", "isShowImage": 0}, {"categoryCode": "126", "categoryLevel": "1", "categoryName": "\"火山\"来袭", "categoryNameEN": "Volcano", "childNode": [{"categoryCode": "127", "categoryLevel": "2", "categoryName": "\"火山\"来袭", "categoryNameEN": "Volcano", "childNode": [], "imgUrl": "", "isShowImage": 0}], "imgUrl": "", "isShowImage": 0}, {"categoryCode": "46", "categoryLevel": "1", "categoryName": "比萨", "categoryNameEN": "Pizza", "childNode": [{"categoryCode": "53", "categoryLevel": "2", "categoryName": "甄选尊享", "categoryNameEN": "INDULGENT", "childNode": [], "imgUrl": "", "isShowImage": 0}, {"categoryCode": "52", "categoryLevel": "2", "categoryName": "经典风味", "categoryNameEN": "CLASSIC", "childNode": [], "imgUrl": "", "isShowImage": 0}, {"categoryCode": "51", "categoryLevel": "2", "categoryName": "物超所值", "categoryNameEN": "VALUE", "childNode": [], "imgUrl": "", "isShowImage": 0}], "imgUrl": "", "isShowImage": 0}, {"categoryCode": "117", "categoryLevel": "1", "categoryName": "牛排/三文鱼", "categoryNameEN": "Steak", "childNode": [{"categoryCode": "118", "categoryLevel": "2", "categoryName": "牛排/三文鱼", "categoryNameEN": "Steak", "childNode": [], "imgUrl": "", "isShowImage": 0}], "imgUrl": "", "isShowImage": 0}, {"categoryCode": "47", "categoryLevel": "1", "categoryName": "面饭", "categoryNameEN": "Pasta & Rice", "childNode": [{"categoryCode": "55", "categoryLevel": "2", "categoryName": "意面类", "categoryNameEN": "PASTA", "childNode": [], "imgUrl": "", "isShowImage": 0}, {"categoryCode": "56", "categoryLevel": "2", "categoryName": "焗饭/炒饭类", "categoryNameEN": "RICE", "childNode": [], "imgUrl": "", "isShowImage": 0}], "imgUrl": "", "isShowImage": 0}, {"categoryCode": "48", "categoryLevel": "1", "categoryName": "小食", "categoryNameEN": "Sides", "childNode": [{"categoryCode": "57", "categoryLevel": "2", "categoryName": "小食类", "categoryNameEN": "Sides", "childNode": [], "imgUrl": "", "isShowImage": 0}], "imgUrl": "", "isShowImage": 0}, {"categoryCode": "49", "categoryLevel": "1", "categoryName": "甜品", "categoryNameEN": "Dessert", "childNode": [{"categoryCode": "58", "categoryLevel": "2", "categoryName": "甜品", "categoryNameEN": "DESSERT", "childNode": [], "imgUrl": "", "isShowImage": 0}], "imgUrl": "", "isShowImage": 0}, {"categoryCode": "63", "categoryLevel": "1", "categoryName": "汤类", "categoryNameEN": "Soups", "childNode": [{"categoryCode": "64", "categoryLevel": "2", "categoryName": "汤类", "categoryNameEN": "Soups", "childNode": [], "imgUrl": "", "isShowImage": 0}], "imgUrl": "", "isShowImage": 0}, {"categoryCode": "61", "categoryLevel": "1", "categoryName": "饮品上新", "categoryNameEN": "Drinks", "childNode": [{"categoryCode": "59", "categoryLevel": "2", "categoryName": "饮料", "categoryNameEN": "DRINKS", "childNode": [], "imgUrl": "", "isShowImage": 0}], "imgUrl": "", "isShowImage": 0}, {"categoryCode": "128", "categoryLevel": "1", "categoryName": "蛋仔派对", "categoryNameEN": "Eggy Party", "childNode": [{"categoryCode": "129", "categoryLevel": "2", "categoryName": "蛋仔派对", "categoryNameEN": "Eggy Party", "childNode": [], "imgUrl": "", "isShowImage": 0}], "imgUrl": "", "isShowImage": 0}, {"categoryCode": "77", "categoryLevel": "1", "categoryName": "个人悠享", "categoryNameEN": "1 Person Combo", "childNode": [{"categoryCode": "78", "categoryLevel": "2", "categoryName": "个人悠享", "categoryNameEN": "1 Person Combo", "childNode": [], "imgUrl": "", "isShowImage": 0}], "imgUrl": "", "isShowImage": 0}, {"categoryCode": "50", "categoryLevel": "1", "categoryName": "套餐优惠", "categoryNameEN": "Combo", "childNode": [{"categoryCode": "60", "categoryLevel": "2", "categoryName": "超值套餐", "categoryNameEN": "Combo", "childNode": [], "imgUrl": "", "isShowImage": 0}], "imgUrl": "", "isShowImage": 0}]